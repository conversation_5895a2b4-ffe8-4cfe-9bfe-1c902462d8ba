<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Deteksi Lokasi - Media Pembelajaran</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            justify-content: center;
            align-items: center;
            padding: 20px;
        }

        .container {
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            padding: 40px;
            max-width: 600px;
            width: 100%;
            text-align: center;
        }

        .header {
            margin-bottom: 30px;
        }

        .header h1 {
            color: #333;
            margin-bottom: 10px;
            font-size: 2.5em;
        }

        .header p {
            color: #666;
            font-size: 1.1em;
        }

        .facebook-link {
            background: #1877f2;
            color: white;
            padding: 15px 30px;
            border: none;
            border-radius: 10px;
            font-size: 1.2em;
            cursor: pointer;
            text-decoration: none;
            display: inline-block;
            margin: 20px 0;
            transition: all 0.3s ease;
        }

        .facebook-link:hover {
            background: #166fe5;
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(24, 119, 242, 0.3);
        }

        .location-info {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
            display: none;
        }

        .location-info.show {
            display: block;
            animation: fadeIn 0.5s ease;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }

        .location-item {
            margin: 10px 0;
            padding: 10px;
            background: white;
            border-radius: 5px;
            border-left: 4px solid #667eea;
        }

        .location-item strong {
            color: #333;
        }

        .loading {
            display: none;
            color: #667eea;
            font-size: 1.1em;
            margin: 20px 0;
        }

        .loading.show {
            display: block;
        }

        .error {
            background: #ffe6e6;
            color: #d63031;
            padding: 15px;
            border-radius: 10px;
            margin: 20px 0;
            display: none;
        }

        .error.show {
            display: block;
        }

        .map-container {
            margin: 20px 0;
            border-radius: 10px;
            overflow: hidden;
            height: 300px;
            display: none;
        }

        .map-container.show {
            display: block;
        }

        .educational-note {
            background: #e8f4fd;
            border: 1px solid #bee5eb;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
            text-align: left;
        }

        .educational-note h3 {
            color: #0c5460;
            margin-bottom: 10px;
        }

        .educational-note p {
            color: #0c5460;
            line-height: 1.6;
            margin-bottom: 10px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🌍 Deteksi Lokasi</h1>
            <p>Media Pembelajaran Geolokasi Web</p>
        </div>

        <a href="https://www.facebook.com/share/19Z9U4vNks/" target="_blank" class="facebook-link" onclick="detectLocation()">
            📱 Klik untuk Akses Facebook & Deteksi Lokasi
        </a>

        <div class="loading" id="loading">
            🔄 Mendeteksi lokasi Anda...
        </div>

        <div class="error" id="error">
            ❌ Tidak dapat mendeteksi lokasi. Pastikan Anda mengizinkan akses lokasi di browser.
        </div>

        <div class="location-info" id="locationInfo">
            <h3>📍 Informasi Lokasi Anda:</h3>
            <div class="location-item">
                <strong>Latitude:</strong> <span id="latitude">-</span>
            </div>
            <div class="location-item">
                <strong>Longitude:</strong> <span id="longitude">-</span>
            </div>
            <div class="location-item">
                <strong>Akurasi:</strong> <span id="accuracy">-</span> meter
            </div>
            <div class="location-item">
                <strong>Waktu Deteksi:</strong> <span id="timestamp">-</span>
            </div>
        </div>

        <div class="map-container" id="mapContainer">
            <iframe id="mapFrame" width="100%" height="300" frameborder="0" style="border:0"></iframe>
        </div>

        <div class="educational-note">
            <h3>📚 Catatan Pembelajaran:</h3>
            <p><strong>Geolocation API:</strong> Fitur browser yang memungkinkan website mendeteksi lokasi pengguna dengan persetujuan.</p>
            <p><strong>Privacy:</strong> Browser akan meminta izin sebelum mengakses lokasi untuk melindungi privasi pengguna.</p>
            <p><strong>Akurasi:</strong> Tingkat akurasi tergantung pada perangkat (GPS, WiFi, atau IP address).</p>
            <p><strong>Penggunaan:</strong> Berguna untuk aplikasi peta, cuaca lokal, pencarian tempat terdekat, dll.</p>
        </div>
    </div>

    <script>
        function detectLocation() {
            // Reset tampilan
            document.getElementById('loading').classList.remove('show');
            document.getElementById('error').classList.remove('show');
            document.getElementById('locationInfo').classList.remove('show');
            document.getElementById('mapContainer').classList.remove('show');

            // Cek apakah browser mendukung geolocation
            if (!navigator.geolocation) {
                showError('Browser Anda tidak mendukung deteksi lokasi.');
                return;
            }

            // Tampilkan loading
            document.getElementById('loading').classList.add('show');

            // Opsi untuk geolocation
            const options = {
                enableHighAccuracy: true,
                timeout: 10000,
                maximumAge: 0
            };

            // Dapatkan lokasi
            navigator.geolocation.getCurrentPosition(
                function(position) {
                    showLocation(position);
                },
                function(error) {
                    handleLocationError(error);
                },
                options
            );
        }

        function showLocation(position) {
            // Sembunyikan loading
            document.getElementById('loading').classList.remove('show');

            // Ambil data lokasi
            const lat = position.coords.latitude;
            const lng = position.coords.longitude;
            const accuracy = Math.round(position.coords.accuracy);
            const timestamp = new Date(position.timestamp).toLocaleString('id-ID');

            // Tampilkan informasi lokasi
            document.getElementById('latitude').textContent = lat.toFixed(6);
            document.getElementById('longitude').textContent = lng.toFixed(6);
            document.getElementById('accuracy').textContent = accuracy;
            document.getElementById('timestamp').textContent = timestamp;
            document.getElementById('locationInfo').classList.add('show');

            // Tampilkan peta
            showMap(lat, lng);

            // Log untuk pembelajaran
            console.log('Lokasi terdeteksi:', {
                latitude: lat,
                longitude: lng,
                accuracy: accuracy,
                timestamp: timestamp
            });
        }

        function showMap(lat, lng) {
            const mapUrl = `https://www.google.com/maps/embed/v1/view?key=AIzaSyBFw0Qbyq9zTFTd-tUY6dOWTgHz-y931Pk&center=${lat},${lng}&zoom=15`;
            document.getElementById('mapFrame').src = mapUrl;
            document.getElementById('mapContainer').classList.add('show');
        }

        function handleLocationError(error) {
            document.getElementById('loading').classList.remove('show');
            
            let errorMessage = '';
            switch(error.code) {
                case error.PERMISSION_DENIED:
                    errorMessage = 'Akses lokasi ditolak oleh pengguna.';
                    break;
                case error.POSITION_UNAVAILABLE:
                    errorMessage = 'Informasi lokasi tidak tersedia.';
                    break;
                case error.TIMEOUT:
                    errorMessage = 'Waktu permintaan lokasi habis.';
                    break;
                default:
                    errorMessage = 'Terjadi kesalahan yang tidak diketahui.';
                    break;
            }
            
            showError(errorMessage);
        }

        function showError(message) {
            document.getElementById('error').textContent = '❌ ' + message;
            document.getElementById('error').classList.add('show');
        }

        // Deteksi otomatis saat halaman dimuat (opsional)
        window.addEventListener('load', function() {
            console.log('Halaman dimuat. Siap untuk mendeteksi lokasi.');
        });
    </script>
</body>
</html>
