<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Admin Panel - Location Tracker</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
            min-height: 100vh;
            color: #333;
        }

        .header {
            background: rgba(255, 255, 255, 0.95);
            padding: 20px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            margin-bottom: 20px;
        }

        .header h1 {
            color: #2c3e50;
            text-align: center;
            font-size: 2.5em;
            margin-bottom: 10px;
        }

        .header p {
            text-align: center;
            color: #7f8c8d;
            font-size: 1.1em;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .stat-card {
            background: white;
            border-radius: 15px;
            padding: 25px;
            text-align: center;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            transition: transform 0.3s ease;
        }

        .stat-card:hover {
            transform: translateY(-5px);
        }

        .stat-card .icon {
            font-size: 3em;
            margin-bottom: 15px;
        }

        .stat-card .number {
            font-size: 2.5em;
            font-weight: bold;
            color: #2c3e50;
            margin-bottom: 10px;
        }

        .stat-card .label {
            color: #7f8c8d;
            font-size: 1.1em;
        }

        .controls {
            background: white;
            border-radius: 15px;
            padding: 25px;
            margin-bottom: 30px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
        }

        .controls h3 {
            margin-bottom: 20px;
            color: #2c3e50;
        }

        .btn {
            background: #3498db;
            color: white;
            border: none;
            padding: 12px 25px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 1em;
            margin-right: 10px;
            margin-bottom: 10px;
            transition: all 0.3s ease;
        }

        .btn:hover {
            background: #2980b9;
            transform: translateY(-2px);
        }

        .btn.danger {
            background: #e74c3c;
        }

        .btn.danger:hover {
            background: #c0392b;
        }

        .btn.success {
            background: #27ae60;
        }

        .btn.success:hover {
            background: #229954;
        }

        .logs-container {
            background: white;
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
        }

        .logs-container h3 {
            margin-bottom: 20px;
            color: #2c3e50;
        }

        .log-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
        }

        .log-table th,
        .log-table td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #ecf0f1;
        }

        .log-table th {
            background: #f8f9fa;
            font-weight: 600;
            color: #2c3e50;
        }

        .log-table tr:hover {
            background: #f8f9fa;
        }

        .ip-badge {
            background: #3498db;
            color: white;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 0.9em;
        }

        .browser-badge {
            background: #27ae60;
            color: white;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 0.9em;
        }

        .loading {
            text-align: center;
            padding: 40px;
            color: #7f8c8d;
        }

        .map-link {
            color: #3498db;
            text-decoration: none;
        }

        .map-link:hover {
            text-decoration: underline;
        }

        .refresh-info {
            background: #e8f4fd;
            border: 1px solid #bee5eb;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 20px;
            color: #0c5460;
        }

        @media (max-width: 768px) {
            .stats-grid {
                grid-template-columns: 1fr;
            }
            
            .log-table {
                font-size: 0.9em;
            }
            
            .log-table th,
            .log-table td {
                padding: 8px;
            }
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>🛡️ Admin Panel</h1>
        <p>Location Tracking Management System</p>
    </div>

    <div class="container">
        <div class="stats-grid">
            <div class="stat-card">
                <div class="icon">📍</div>
                <div class="number" id="totalLogs">-</div>
                <div class="label">Total Deteksi</div>
            </div>
            <div class="stat-card">
                <div class="icon">👥</div>
                <div class="number" id="uniqueIPs">-</div>
                <div class="label">IP Unik</div>
            </div>
            <div class="stat-card">
                <div class="icon">🌐</div>
                <div class="number" id="topBrowser">-</div>
                <div class="label">Browser Terpopuler</div>
            </div>
            <div class="stat-card">
                <div class="icon">📅</div>
                <div class="number" id="todayCount">-</div>
                <div class="label">Hari Ini</div>
            </div>
        </div>

        <div class="controls">
            <h3>🔧 Kontrol Admin</h3>
            <div class="refresh-info">
                ℹ️ Data akan diperbarui otomatis setiap 30 detik. Klik "Refresh Data" untuk update manual.
            </div>
            <button class="btn" onclick="refreshData()">🔄 Refresh Data</button>
            <button class="btn success" onclick="exportLogs()">📥 Export CSV</button>
            <button class="btn danger" onclick="clearLogs()">🗑️ Hapus Semua Log</button>
        </div>

        <div class="logs-container">
            <h3>📋 Log Lokasi Terbaru</h3>
            <div id="logsContent">
                <div class="loading">🔄 Memuat data...</div>
            </div>
        </div>
    </div>

    <script>
        let autoRefreshInterval;

        // Load data saat halaman dimuat
        window.addEventListener('load', function() {
            loadLogs();
            startAutoRefresh();
        });

        function startAutoRefresh() {
            // Auto refresh setiap 30 detik
            autoRefreshInterval = setInterval(loadLogs, 30000);
        }

        function stopAutoRefresh() {
            if (autoRefreshInterval) {
                clearInterval(autoRefreshInterval);
            }
        }

        function refreshData() {
            loadLogs();
        }

        function loadLogs() {
            fetch('/admin/logs')
                .then(response => response.json())
                .then(data => {
                    displayLogs(data);
                    updateStats(data);
                })
                .catch(error => {
                    console.error('Error loading logs:', error);
                    document.getElementById('logsContent').innerHTML = 
                        '<div class="loading">❌ Error memuat data</div>';
                });
        }

        function displayLogs(logs) {
            if (logs.length === 0) {
                document.getElementById('logsContent').innerHTML = 
                    '<div class="loading">📭 Belum ada data lokasi</div>';
                return;
            }

            let html = `
                <table class="log-table">
                    <thead>
                        <tr>
                            <th>Waktu</th>
                            <th>IP Address</th>
                            <th>Lokasi</th>
                            <th>Akurasi</th>
                            <th>Browser</th>
                            <th>OS</th>
                            <th>Aksi</th>
                        </tr>
                    </thead>
                    <tbody>
            `;

            logs.forEach(log => {
                const mapsUrl = `https://www.google.com/maps?q=${log.latitude},${log.longitude}`;
                html += `
                    <tr>
                        <td>${new Date(log.created_at).toLocaleString('id-ID')}</td>
                        <td><span class="ip-badge">${log.real_ip || log.client_ip}</span></td>
                        <td>${log.latitude?.toFixed(6)}, ${log.longitude?.toFixed(6)}</td>
                        <td>${log.accuracy}m</td>
                        <td><span class="browser-badge">${log.browser}</span></td>
                        <td>${log.os}</td>
                        <td><a href="${mapsUrl}" target="_blank" class="map-link">🗺️ Lihat</a></td>
                    </tr>
                `;
            });

            html += '</tbody></table>';
            document.getElementById('logsContent').innerHTML = html;
        }

        function updateStats(logs) {
            // Total logs
            document.getElementById('totalLogs').textContent = logs.length;

            // Unique IPs
            const uniqueIPs = new Set(logs.map(log => log.real_ip || log.client_ip));
            document.getElementById('uniqueIPs').textContent = uniqueIPs.size;

            // Top browser
            const browsers = {};
            logs.forEach(log => {
                browsers[log.browser] = (browsers[log.browser] || 0) + 1;
            });
            const topBrowser = Object.keys(browsers).reduce((a, b) => 
                browsers[a] > browsers[b] ? a : b, 'N/A');
            document.getElementById('topBrowser').textContent = topBrowser;

            // Today count
            const today = new Date().toDateString();
            const todayLogs = logs.filter(log => 
                new Date(log.created_at).toDateString() === today);
            document.getElementById('todayCount').textContent = todayLogs.length;
        }

        function exportLogs() {
            window.open('/admin/export', '_blank');
        }

        function clearLogs() {
            if (confirm('Apakah Anda yakin ingin menghapus semua log? Tindakan ini tidak dapat dibatalkan.')) {
                fetch('/admin/clear', { method: 'POST' })
                    .then(response => response.json())
                    .then(data => {
                        alert('Log berhasil dihapus');
                        loadLogs();
                    })
                    .catch(error => {
                        alert('Error menghapus log: ' + error);
                    });
            }
        }

        // Stop auto refresh saat halaman tidak aktif
        document.addEventListener('visibilitychange', function() {
            if (document.hidden) {
                stopAutoRefresh();
            } else {
                startAutoRefresh();
            }
        });
    </script>
</body>
</html>
