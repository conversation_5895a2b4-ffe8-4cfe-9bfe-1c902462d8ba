#!/usr/bin/env python3
"""
Server se<PERSON>hana untuk menjalankan aplikasi deteksi lokasi
Media pembelajaran geolokasi web
"""

import http.server
import socketserver
import webbrowser
import os
import sys
import json
import urllib.parse
from pathlib import Path
from datetime import datetime

class CustomHTTPRequestHandler(http.server.SimpleHTTPRequestHandler):
    def end_headers(self):
        self.send_header('Access-Control-Allow-Origin', '*')
        self.send_header('Access-Control-Allow-Methods', 'GET, POST, OPTIONS')
        self.send_header('Access-Control-Allow-Headers', 'Content-Type')
        super().end_headers()

    def do_POST(self):
        if self.path == '/location':
            self.handle_location_data()
        else:
            self.send_error(404, "Not Found")

    def do_OPTIONS(self):
        self.send_response(200)
        self.end_headers()

    def get_real_ip_address(self):
        """Deteksi IP address yang lebih akurat dengan memeriksa berbagai header"""
        # Cek header yang umum digunakan untuk IP forwarding
        ip_headers = [
            'X-Forwarded-For',
            'X-Real-IP',
            'X-Client-IP',
            'CF-Connecting-IP',  # Cloudflare
            'True-Client-IP',    # Akamai
            'X-Forwarded',
            'Forwarded-For',
            'Forwarded'
        ]

        # Cek setiap header
        for header in ip_headers:
            if header in self.headers:
                ip = self.headers[header].split(',')[0].strip()
                if self.is_valid_ip(ip):
                    return ip

        # Fallback ke client address
        return self.client_address[0]

    def is_valid_ip(self, ip):
        """Validasi apakah string adalah IP address yang valid"""
        try:
            parts = ip.split('.')
            if len(parts) != 4:
                return False
            for part in parts:
                if not 0 <= int(part) <= 255:
                    return False
            return True
        except:
            return False

    def handle_location_data(self):
        try:
            content_length = int(self.headers['Content-Length'])
            post_data = self.rfile.read(content_length)
            location_data = json.loads(post_data.decode('utf-8'))

            client_ip = self.client_address[0]
            location_data['client_ip'] = client_ip

            
            real_ip = self.get_real_ip_address()
            location_data['real_ip'] = real_ip

            
            location_data['headers'] = dict(self.headers)

            self.display_location_info(location_data)

            response = {
                'status': 'success',
                'message': 'Location data received',
                'timestamp': datetime.now().isoformat()
            }

            self.send_response(200)
            self.send_header('Content-Type', 'application/json')
            self.end_headers()
            self.wfile.write(json.dumps(response).encode('utf-8'))

        except Exception as e:
            error_response = {
                'status': 'error',
                'message': str(e)
            }

            self.send_response(500)
            self.send_header('Content-Type', 'application/json')
            self.end_headers()
            self.wfile.write(json.dumps(error_response).encode('utf-8'))

    def display_location_info(self, data):
        print("\n" + "="*80)
        print("🌍 LOKASI TERDETEKSI!")
        print("="*80)
        print(f"📅 Waktu: {data.get('timestamp', 'N/A')}")

        # Tampilkan informasi IP yang lebih lengkap
        client_ip = data.get('client_ip', 'N/A')
        real_ip = data.get('real_ip', 'N/A')

        print(f"🌐 IP Address (Direct): {client_ip}")
        if real_ip != client_ip:
            print(f"🌐 IP Address (Real): {real_ip}")

        # Analisis jenis IP
        ip_type = self.analyze_ip_type(real_ip)
        print(f"� Jenis IP: {ip_type}")

        print(f"�📍 Latitude: {data.get('latitude', 'N/A')}")
        print(f"📍 Longitude: {data.get('longitude', 'N/A')}")
        print(f"🎯 Akurasi: {data.get('accuracy', 'N/A')} meter")

        # Tampilkan link Google Maps
        if data.get('latitude') and data.get('longitude'):
            maps_url = f"https://www.google.com/maps?q={data['latitude']},{data['longitude']}"
            print(f"🗺️  Google Maps: {maps_url}")

        # Tampilkan info browser dan OS
        user_agent = data.get('userAgent', '')
        browser_info = self.parse_user_agent(user_agent)
        print(f"🌐 Browser: {browser_info['browser']}")
        print(f"💻 OS: {browser_info['os']}")

        # Tampilkan header penting untuk debugging
        headers = data.get('headers', {})
        important_headers = ['X-Forwarded-For', 'X-Real-IP', 'CF-Connecting-IP', 'User-Agent']
        for header in important_headers:
            if header in headers and header != 'User-Agent':
                print(f"📋 {header}: {headers[header]}")

        print("="*80)
        print("🔄 Menunggu koneksi berikutnya...\n")

    def analyze_ip_type(self, ip):
        """Analisis jenis IP address"""
        if ip == 'N/A':
            return 'Unknown'

        try:
            parts = [int(x) for x in ip.split('.')]

            # Private IP ranges
            if (parts[0] == 10 or
                (parts[0] == 172 and 16 <= parts[1] <= 31) or
                (parts[0] == 192 and parts[1] == 168)):
                return 'Private/Local Network'

            # Localhost
            if parts[0] == 127:
                return 'Localhost'

            # Public IP
            return 'Public Internet'

        except:
            return 'Invalid IP'

    def parse_user_agent(self, user_agent):
        """Parse user agent untuk mendapatkan info browser dan OS"""
        browser = 'Unknown'
        os = 'Unknown'

        # Deteksi Browser
        if 'Chrome' in user_agent and 'Edg' not in user_agent:
            browser = 'Chrome'
        elif 'Firefox' in user_agent:
            browser = 'Firefox'
        elif 'Safari' in user_agent and 'Chrome' not in user_agent:
            browser = 'Safari'
        elif 'Edg' in user_agent:
            browser = 'Microsoft Edge'
        elif 'Opera' in user_agent:
            browser = 'Opera'

        # Deteksi OS
        if 'Windows NT 10' in user_agent:
            os = 'Windows 10/11'
        elif 'Windows NT 6.3' in user_agent:
            os = 'Windows 8.1'
        elif 'Windows NT 6.1' in user_agent:
            os = 'Windows 7'
        elif 'Mac OS X' in user_agent:
            os = 'macOS'
        elif 'Android' in user_agent:
            os = 'Android'
        elif 'iPhone' in user_agent:
            os = 'iOS (iPhone)'
        elif 'iPad' in user_agent:
            os = 'iOS (iPad)'
        elif 'Linux' in user_agent:
            os = 'Linux'

        return {'browser': browser, 'os': os}

    def log_message(self, format, *args):
        # Custom logging
        if not self.path.startswith('/location'):
            print(f"[{self.address_string()}] {format % args}")

def main():
    # Konfigurasi server
    PORT = 8000
    HOST = 'localhost'
    
    # Pastikan kita berada di direktori yang benar
    script_dir = Path(__file__).parent
    os.chdir(script_dir)
    
    print("=" * 60)
    print("🌍 SERVER DETEKSI LOKASI - MEDIA PEMBELAJARAN")
    print("=" * 60)
    print(f"📂 Direktori: {script_dir}")
    print(f"🌐 Server: http://{HOST}:{PORT}")
    print(f"📱 Akses dari perangkat lain: http://[IP_KOMPUTER_ANDA]:{PORT}")
    print("=" * 60)
    
    # Cek apakah file index.html ada
    if not Path('index.html').exists():
        print("❌ Error: File index.html tidak ditemukan!")
        print("   Pastikan file index.html ada di direktori yang sama dengan server.py")
        sys.exit(1)
    
    try:
        # Buat server
        with socketserver.TCPServer((HOST, PORT), CustomHTTPRequestHandler) as httpd:
            print(f"✅ Server berjalan di http://{HOST}:{PORT}")
            print("\n📋 INSTRUKSI PENGGUNAAN:")
            print("1. Buka browser dan akses URL di atas")
            print("2. Klik tombol 'Klik untuk Akses Facebook & Deteksi Lokasi'")
            print("3. Izinkan browser mengakses lokasi Anda")
            print("4. Lihat informasi lokasi yang terdeteksi")
            print("\n🔧 UNTUK PEMBELAJARAN:")
            print("- Buka Developer Tools (F12) untuk melihat console log")
            print("- Perhatikan bagaimana Geolocation API bekerja")
            print("- Coba akses dari perangkat berbeda untuk melihat perbedaan akurasi")
            print("\n⏹️  Tekan Ctrl+C untuk menghentikan server")
            print("=" * 60)
            
            # Buka browser secara otomatis
            try:
                webbrowser.open(f'http://{HOST}:{PORT}')
                print("🌐 Browser dibuka secara otomatis")
            except:
                print("⚠️  Tidak dapat membuka browser secara otomatis")
                print(f"   Silakan buka manual: http://{HOST}:{PORT}")
            
            print("\n🔄 Menunggu koneksi...")
            
            # Jalankan server
            httpd.serve_forever()
            
    except KeyboardInterrupt:
        print("\n\n⏹️  Server dihentikan oleh pengguna")
    except OSError as e:
        if e.errno == 10048:  # Port sudah digunakan
            print(f"❌ Error: Port {PORT} sudah digunakan!")
            print("   Coba gunakan port lain atau hentikan aplikasi yang menggunakan port tersebut")
        else:
            print(f"❌ Error: {e}")
    except Exception as e:
        print(f"❌ Error tidak terduga: {e}")

if __name__ == "__main__":
    main()
