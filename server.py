#!/usr/bin/env python3
"""
Server sederhana untuk menjalankan aplikasi deteksi lokasi
Media pembelajaran geolokasi web
"""

import http.server
import socketserver
import webbrowser
import os
import sys
from pathlib import Path

class CustomHTTPRequestHandler(http.server.SimpleHTTPRequestHandler):
    def end_headers(self):
        # Tambahkan header CORS untuk mengizinkan akses dari berbagai domain
        self.send_header('Access-Control-Allow-Origin', '*')
        self.send_header('Access-Control-Allow-Methods', 'GET, POST, OPTIONS')
        self.send_header('Access-Control-Allow-Headers', 'Content-Type')
        super().end_headers()

    def log_message(self, format, *args):
        # Custom logging
        print(f"[{self.address_string()}] {format % args}")

def main():
    # Konfigurasi server
    PORT = 8000
    HOST = 'localhost'
    
    # Pastikan kita berada di direktori yang benar
    script_dir = Path(__file__).parent
    os.chdir(script_dir)
    
    print("=" * 60)
    print("🌍 SERVER DETEKSI LOKASI - MEDIA PEMBELAJARAN")
    print("=" * 60)
    print(f"📂 Direktori: {script_dir}")
    print(f"🌐 Server: http://{HOST}:{PORT}")
    print(f"📱 Akses dari perangkat lain: http://[IP_KOMPUTER_ANDA]:{PORT}")
    print("=" * 60)
    
    # Cek apakah file index.html ada
    if not Path('index.html').exists():
        print("❌ Error: File index.html tidak ditemukan!")
        print("   Pastikan file index.html ada di direktori yang sama dengan server.py")
        sys.exit(1)
    
    try:
        # Buat server
        with socketserver.TCPServer((HOST, PORT), CustomHTTPRequestHandler) as httpd:
            print(f"✅ Server berjalan di http://{HOST}:{PORT}")
            print("\n📋 INSTRUKSI PENGGUNAAN:")
            print("1. Buka browser dan akses URL di atas")
            print("2. Klik tombol 'Klik untuk Akses Facebook & Deteksi Lokasi'")
            print("3. Izinkan browser mengakses lokasi Anda")
            print("4. Lihat informasi lokasi yang terdeteksi")
            print("\n🔧 UNTUK PEMBELAJARAN:")
            print("- Buka Developer Tools (F12) untuk melihat console log")
            print("- Perhatikan bagaimana Geolocation API bekerja")
            print("- Coba akses dari perangkat berbeda untuk melihat perbedaan akurasi")
            print("\n⏹️  Tekan Ctrl+C untuk menghentikan server")
            print("=" * 60)
            
            # Buka browser secara otomatis
            try:
                webbrowser.open(f'http://{HOST}:{PORT}')
                print("🌐 Browser dibuka secara otomatis")
            except:
                print("⚠️  Tidak dapat membuka browser secara otomatis")
                print(f"   Silakan buka manual: http://{HOST}:{PORT}")
            
            print("\n🔄 Menunggu koneksi...")
            
            # Jalankan server
            httpd.serve_forever()
            
    except KeyboardInterrupt:
        print("\n\n⏹️  Server dihentikan oleh pengguna")
    except OSError as e:
        if e.errno == 10048:  # Port sudah digunakan
            print(f"❌ Error: Port {PORT} sudah digunakan!")
            print("   Coba gunakan port lain atau hentikan aplikasi yang menggunakan port tersebut")
        else:
            print(f"❌ Error: {e}")
    except Exception as e:
        print(f"❌ Error tidak terduga: {e}")

if __name__ == "__main__":
    main()
