@echo off
echo ========================================
echo 🚀 DEPLOY APLIKASI LOCATION TRACKER
echo ========================================

echo.
echo Pilih platform deploy:
echo 1. Heroku
echo 2. Railway  
echo 3. Render
echo 4. Manual Setup
echo.

set /p choice="Masukkan pilihan (1-4): "

if "%choice%"=="1" goto heroku
if "%choice%"=="2" goto railway
if "%choice%"=="3" goto render
if "%choice%"=="4" goto manual
goto end

:heroku
echo.
echo 📦 Deploy ke Heroku...
echo.
set /p appname="Masukkan nama aplikasi: "

echo Checking Heroku CLI...
heroku --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Heroku CLI tidak terinstall!
    echo Download dari: https://devcenter.heroku.com/articles/heroku-cli
    goto end
)

echo Login ke Heroku...
heroku login

echo Membuat aplikasi...
heroku create %appname%

echo Initialize Git...
git init
git add .
git commit -m "Deploy location tracker"

echo Deploy ke Heroku...
git push heroku main

echo.
echo ✅ Deploy selesai!
echo 🌐 URL: https://%appname%.herokuapp.com
echo 🛡️ Admin: https://%appname%.herokuapp.com/admin
goto end

:railway
echo.
echo 🚂 Deploy ke Railway...
echo.
echo 1. Buka https://railway.app
echo 2. Login dengan GitHub
echo 3. Klik "New Project"
echo 4. Pilih "Deploy from GitHub repo"
echo 5. Upload project ini ke GitHub dulu
echo 6. Pilih repository
echo 7. Deploy otomatis!
echo.
echo ✅ Instruksi ditampilkan!
goto end

:render
echo.
echo 🎨 Deploy ke Render...
echo.
echo 1. Buka https://render.com
echo 2. Login dengan GitHub
echo 3. Klik "New Web Service"
echo 4. Connect repository
echo 5. Set:
echo    - Build Command: pip install -r requirements.txt
echo    - Start Command: python server.py
echo 6. Deploy!
echo.
echo ✅ Instruksi ditampilkan!
goto end

:manual
echo.
echo 🔧 Manual Setup...
echo.
echo File yang dibutuhkan sudah dibuat:
echo - requirements.txt
echo - Procfile
echo - runtime.txt
echo - railway.json
echo - DEPLOY.md (panduan lengkap)
echo.
echo Baca DEPLOY.md untuk panduan detail!
goto end

:end
echo.
echo 📚 Untuk panduan lengkap, baca file DEPLOY.md
echo ⚡ Server lokal: python server.py
echo.
pause
