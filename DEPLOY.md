# 🚀 Panduan Deploy Online

## 1. Deploy ke Heroku (GRATIS)

### Persiapan:
1. Daftar akun di [heroku.com](https://heroku.com)
2. Install Heroku CLI dari [devcenter.heroku.com/articles/heroku-cli](https://devcenter.heroku.com/articles/heroku-cli)
3. Install Git jika belum ada

### Langkah Deploy:

```bash
# 1. Login ke Heroku
heroku login

# 2. Buat aplikasi baru
heroku create nama-aplikasi-anda

# 3. Initialize git (jika belum)
git init
git add .
git commit -m "Initial commit"

# 4. Deploy ke Heroku
git push heroku main

# 5. Buka aplikasi
heroku open
```

### URL Hasil:
- Aplikasi: `https://nama-aplikasi-anda.herokuapp.com`
- Admin: `https://nama-aplikasi-anda.herokuapp.com/admin`

---

## 2. Deploy ke Railway (GRATIS)

### Langkah Deploy:
1. Daftar di [railway.app](https://railway.app)
2. Connect dengan GitHub
3. Upload project ke GitHub
4. Deploy dari Railway dashboard
5. Set environment variables jika perlu

---

## 3. Deploy ke Render (GRATIS)

### Langkah Deploy:
1. Daftar di [render.com](https://render.com)
2. Connect GitHub repository
3. Pilih "Web Service"
4. Set build command: `pip install -r requirements.txt`
5. Set start command: `python server.py`

---

## 4. Deploy ke Vercel (GRATIS)

### Persiapan:
Buat file `vercel.json`:

```json
{
  "version": 2,
  "builds": [
    {
      "src": "server.py",
      "use": "@vercel/python"
    }
  ],
  "routes": [
    {
      "src": "/(.*)",
      "dest": "server.py"
    }
  ]
}
```

### Langkah Deploy:
1. Install Vercel CLI: `npm i -g vercel`
2. Login: `vercel login`
3. Deploy: `vercel --prod`

---

## 5. Deploy ke PythonAnywhere (GRATIS)

### Langkah Deploy:
1. Daftar di [pythonanywhere.com](https://pythonanywhere.com)
2. Upload files via dashboard
3. Setup web app dengan Python
4. Configure WSGI file

---

## 🔧 Environment Variables

Untuk production, set environment variables:

```bash
# Heroku
heroku config:set HOST=0.0.0.0
heroku config:set PORT=80

# Railway/Render
HOST=0.0.0.0
PORT=8000
```

---

## 🌐 Custom Domain (Opsional)

### Untuk Heroku:
```bash
heroku domains:add www.domain-anda.com
```

### Untuk Railway:
- Masuk ke dashboard
- Settings → Domains
- Add custom domain

---

## 📱 Testing Online

Setelah deploy:

1. **Test aplikasi utama:**
   - Buka URL aplikasi
   - Test deteksi lokasi
   - Pastikan data tersimpan

2. **Test admin panel:**
   - Akses `/admin`
   - Login dengan admin:admin123
   - Test semua fitur

3. **Test dari perangkat berbeda:**
   - HP, tablet, komputer lain
   - Berbagai browser
   - Berbagai jaringan

---

## 🔒 Keamanan Production

### Ubah Password Admin:
Edit di `server.py` line ~346:
```python
def check_admin_auth(self, auth_header):
    # Ganti password default
    expected = "Basic " + hashlib.md5("admin:PASSWORD_BARU".encode()).hexdigest()
```

### HTTPS:
- Heroku/Railway/Render otomatis provide HTTPS
- Untuk custom domain, setup SSL certificate

---

## 💾 Database Production

### SQLite (Default):
- Cocok untuk testing
- Data hilang saat restart di beberapa platform

### PostgreSQL (Recommended):
```bash
# Heroku
heroku addons:create heroku-postgresql:hobby-dev

# Update code untuk gunakan PostgreSQL
pip install psycopg2-binary
```

---

## 📊 Monitoring

### Heroku Logs:
```bash
heroku logs --tail
```

### Railway Logs:
- Dashboard → Deployments → View Logs

### Render Logs:
- Dashboard → Service → Logs

---

## 🎯 Rekomendasi Platform

**Untuk Pemula:** Heroku atau Railway
**Untuk Performance:** Render atau DigitalOcean
**Untuk Custom:** VPS (DigitalOcean, Linode)

**Gratis Terbaik:**
1. Railway (paling mudah)
2. Render (performance bagus)
3. Heroku (populer, dokumentasi lengkap)
